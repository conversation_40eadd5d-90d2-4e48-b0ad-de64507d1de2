const { faker } = require('faker');
const { v4: uuidv4 } = require('uuid');

class TestDataGenerator {
  constructor() {
    this.emailDomain = process.env.TEST_EMAIL_DOMAIN || 'test.atma.local';
    this.password = process.env.TEST_PASSWORD || 'TestPassword123!';
  }

  generateRandomEmail() {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 8);
    return `test-${timestamp}-${randomId}@${this.emailDomain}`;
  }

  generateUsername() {
    const timestamp = Date.now().toString().slice(-6);
    const randomName = faker.internet.userName().toLowerCase();
    return `${randomName}${timestamp}`;
  }

  generateUserRegistrationData() {
    return {
      email: this.generateRandomEmail(),
      password: this.password,
      username: this.generateUsername()
    };
  }

  generateProfileUpdateData() {
    return {
      username: this.generateUsername(),
      full_name: faker.person.fullName(),
      bio: faker.lorem.sentence(),
      location: faker.location.city(),
      website: faker.internet.url()
    };
  }

  generateAssessmentData() {
    // Generate RIASEC scores (0-100)
    const riasec = {
      realistic: this.randomScore(),
      investigative: this.randomScore(),
      artistic: this.randomScore(),
      social: this.randomScore(),
      enterprising: this.randomScore(),
      conventional: this.randomScore()
    };

    // Generate OCEAN scores (0-100)
    const ocean = {
      openness: this.randomScore(),
      conscientiousness: this.randomScore(),
      extraversion: this.randomScore(),
      agreeableness: this.randomScore(),
      neuroticism: this.randomScore()
    };

    // Generate VIA-IS scores (0-100)
    const viaIs = {
      creativity: this.randomScore(),
      curiosity: this.randomScore(),
      judgment: this.randomScore(),
      loveOfLearning: this.randomScore(),
      perspective: this.randomScore(),
      bravery: this.randomScore(),
      perseverance: this.randomScore(),
      honesty: this.randomScore(),
      zest: this.randomScore(),
      love: this.randomScore(),
      kindness: this.randomScore(),
      socialIntelligence: this.randomScore(),
      teamwork: this.randomScore(),
      fairness: this.randomScore(),
      leadership: this.randomScore(),
      forgiveness: this.randomScore(),
      humility: this.randomScore(),
      prudence: this.randomScore(),
      selfRegulation: this.randomScore(),
      appreciationOfBeauty: this.randomScore(),
      gratitude: this.randomScore(),
      hope: this.randomScore(),
      humor: this.randomScore(),
      spirituality: this.randomScore()
    };

    return {
      assessmentName: process.env.DEFAULT_ASSESSMENT_NAME || 'AI-Driven Talent Mapping',
      riasec,
      ocean,
      viaIs
    };
  }

  randomScore(min = 40, max = 95) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  generateChatMessages() {
    return [
      "Hello! I just completed my assessment and would like some career guidance.",
      "What career paths would be best suited for my personality type?",
      "Can you help me understand my strengths and how to leverage them?",
      "What skills should I focus on developing based on my assessment results?",
      "Thank you for the guidance! This has been very helpful."
    ];
  }

  generateConversationData() {
    return {
      title: `E2E Test Conversation - ${Date.now()}`,
      context_type: "assessment",
      context_data: {},
      metadata: {
        test_run: true,
        timestamp: new Date().toISOString()
      }
    };
  }

  generateTestUser(index = 1) {
    const userData = this.generateUserRegistrationData();
    return {
      id: index,
      email: userData.email,
      password: userData.password,
      username: userData.username,
      profileData: this.generateProfileUpdateData(),
      assessmentData: this.generateAssessmentData(),
      chatMessages: this.generateChatMessages(),
      conversationData: this.generateConversationData()
    };
  }

  generateTestUsers(count = 2) {
    const users = [];
    for (let i = 1; i <= count; i++) {
      users.push(this.generateTestUser(i));
    }
    return users;
  }

  // Validation helpers
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  isValidJWT(token) {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    return jwtRegex.test(token);
  }

  // Assessment validation
  validateAssessmentScores(scores, category) {
    const requiredFields = {
      riasec: ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'],
      ocean: ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'],
      viaIs: ['creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective', 'bravery', 
              'perseverance', 'honesty', 'zest', 'love', 'kindness', 'socialIntelligence', 
              'teamwork', 'fairness', 'leadership', 'forgiveness', 'humility', 'prudence', 
              'selfRegulation', 'appreciationOfBeauty', 'gratitude', 'hope', 'humor', 'spirituality']
    };

    const fields = requiredFields[category];
    if (!fields) return false;

    return fields.every(field => {
      const value = scores[field];
      return typeof value === 'number' && value >= 0 && value <= 100;
    });
  }
}

module.exports = TestDataGenerator;
