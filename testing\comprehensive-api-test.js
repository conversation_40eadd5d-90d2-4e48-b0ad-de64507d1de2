#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const ResponseValidator = require('./lib/validators');

class ComprehensiveAPITest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA Comprehensive API Test - All Endpoints');
    
    try {
      // Setup test user
      await this.setupTestUser();

      // Test Authentication endpoints
      await this.testAuthenticationEndpoints();

      // Test Assessment endpoints
      await this.testAssessmentEndpoints();

      // Test Archive endpoints
      await this.testArchiveEndpoints();

      // Test Chatbot endpoints
      await this.testChatbotEndpoints();

      // Test Health endpoints
      await this.testHealthEndpoints();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestUser() {
    this.logger.step(1, 6, 'Setting up test user');
    
    this.user = this.testData.generateTestUser(1);
    
    // Register user
    const registerResponse = await this.api.register({
      email: this.user.email,
      password: this.user.password,
      username: this.user.username
    });

    this.user.id = registerResponse.data.user.id;
    this.user.token = registerResponse.data.token;
    this.api.setAuthToken(this.user.token);
    
    this.logger.success('Test user setup completed');
  }

  async testAuthenticationEndpoints() {
    this.logger.step(2, 6, 'Testing Authentication endpoints');
    
    try {
      // Test profile retrieval
      await this.testEndpoint('GET /auth/profile', async () => {
        const response = await this.api.getProfile();
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test profile update
      await this.testEndpoint('PUT /auth/profile', async () => {
        const response = await this.api.updateProfile(this.user.profileData);
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test password change
      await this.testEndpoint('POST /auth/change-password', async () => {
        const response = await this.api.changePassword({
          currentPassword: this.user.password,
          newPassword: this.user.password + '2'
        });
        this.validator.validate(response, 'auth');
        
        // Change back to original password
        await this.api.changePassword({
          currentPassword: this.user.password + '2',
          newPassword: this.user.password
        });
        
        return response;
      });

      // Test token balance
      await this.testEndpoint('GET /auth/token-balance', async () => {
        const response = await this.api.getTokenBalance();
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test schools endpoints
      await this.testEndpoint('GET /auth/schools', async () => {
        const response = await this.api.getSchools({ limit: 5 });
        this.validator.validate(response, 'auth');
        
        // Validate pagination structure
        if (response.data.pagination) {
          const paginationErrors = this.validator.validatePaginationStructure(response.data.pagination);
          if (paginationErrors.length > 0) {
            throw new Error(`Pagination validation failed: ${paginationErrors.join(', ')}`);
          }
        }
        
        return response;
      });

      this.logger.success('Authentication endpoints test completed');
      
    } catch (error) {
      this.logger.error('Authentication endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Auth endpoints: ${error.message}`);
    }
  }

  async testAssessmentEndpoints() {
    this.logger.step(3, 6, 'Testing Assessment endpoints');
    
    try {
      // Submit assessment
      const submitResponse = await this.testEndpoint('POST /assessment/submit', async () => {
        const response = await this.api.submitAssessment(this.user.assessmentData);
        this.validator.validate(response, 'assessment-submission');
        return response;
      });

      this.user.jobId = submitResponse.data.jobId;

      // Check assessment status
      await this.testEndpoint('GET /assessment/status/:jobId', async () => {
        const response = await this.api.getAssessmentStatus(this.user.jobId);
        this.validator.validate(response, 'assessment-submission');
        return response;
      });

      // Test health endpoints
      await this.testEndpoint('GET /assessment/health', async () => {
        const response = await this.api.healthCheck();
        return response;
      });

      this.logger.success('Assessment endpoints test completed');
      
    } catch (error) {
      this.logger.error('Assessment endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Assessment endpoints: ${error.message}`);
    }
  }

  async testArchiveEndpoints() {
    this.logger.step(4, 6, 'Testing Archive endpoints');
    
    try {
      // Test results endpoints
      await this.testEndpoint('GET /archive/results', async () => {
        const response = await this.api.getResults({ limit: 5 });
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test jobs endpoints
      await this.testEndpoint('GET /archive/jobs', async () => {
        const response = await this.api.getJobs({ limit: 5 });
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test job statistics
      await this.testEndpoint('GET /archive/jobs/stats', async () => {
        const response = await this.api.getJobStats();
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test archive statistics
      await this.testEndpoint('GET /archive/v1/stats', async () => {
        const response = await this.api.getArchiveStats('user', 'overview');
        this.validator.validate(response, 'auth');
        return response;
      });

      this.logger.success('Archive endpoints test completed');
      
    } catch (error) {
      this.logger.error('Archive endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Archive endpoints: ${error.message}`);
    }
  }

  async testChatbotEndpoints() {
    this.logger.step(5, 6, 'Testing Chatbot endpoints');
    
    try {
      // Test conversations list
      await this.testEndpoint('GET /chatbot/conversations', async () => {
        const response = await this.api.getConversations({ limit: 5 });
        this.validator.validate(response, 'chatbot');
        return response;
      });

      // Test assessment readiness check
      await this.testEndpoint('GET /chatbot/assessment-ready/:userId', async () => {
        const response = await this.api.checkAssessmentReady(this.user.id);
        this.validator.validate(response, 'chatbot');
        return response;
      });

      this.logger.success('Chatbot endpoints test completed');
      
    } catch (error) {
      this.logger.error('Chatbot endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Chatbot endpoints: ${error.message}`);
    }
  }

  async testHealthEndpoints() {
    this.logger.step(6, 6, 'Testing Health endpoints');
    
    try {
      // Test main health check
      await this.testEndpoint('GET /health', async () => {
        const response = await this.api.healthCheck();
        return response;
      });

      // Test notification health check
      await this.testEndpoint('GET /notifications/health', async () => {
        const response = await this.api.notificationHealthCheck();
        return response;
      });

      this.logger.success('Health endpoints test completed');
      
    } catch (error) {
      this.logger.error('Health endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Health endpoints: ${error.message}`);
    }
  }

  async testEndpoint(name, testFunction) {
    this.testResults.total++;
    
    try {
      this.logger.info(`Testing ${name}`);
      const response = await testFunction();
      
      // Validate common response structure
      if (response && typeof response === 'object') {
        if (response.success === undefined) {
          this.logger.warn(`${name}: Response missing 'success' field`);
        }
        if (response.success && !response.timestamp) {
          this.logger.warn(`${name}: Successful response missing 'timestamp' field`);
        }
      }
      
      this.logger.success(`${name}: PASSED`);
      this.testResults.passed++;
      return response;
      
    } catch (error) {
      this.logger.error(`${name}: FAILED - ${error.message}`);
      this.testResults.failed++;
      this.testResults.errors.push(`${name}: ${error.message}`);
      throw error;
    }
  }

  async cleanup() {
    this.logger.info('Performing cleanup...');
    
    try {
      if (this.user && this.user.token) {
        this.api.setAuthToken(this.user.token);
        
        // Logout user
        try {
          await this.api.logout();
          this.logger.info('User logged out');
        } catch (error) {
          this.logger.warn('Failed to logout user:', error.message);
        }
      }

      this.api.setAuthToken(null);
      this.logger.success('Cleanup completed');
      
    } catch (error) {
      this.logger.error('Cleanup failed:', error.message);
    }
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('COMPREHENSIVE API TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All API endpoints tested successfully!');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new ComprehensiveAPITest();
  test.runTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ComprehensiveAPITest;
